[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "langextract-databricksprovider"
version = "0.1.0"
description = "LangExtract provider plugin for DatabricksProvider"
readme = "README.md"
requires-python = ">=3.10"
license = {text = "Apache-2.0"}
dependencies = [
    "langextract>=1.0.0",
    # Add your provider's SDK dependencies here
]

[project.entry-points."langextract.providers"]
databricksprovider = "langextract_databricksprovider.provider:DatabricksProviderLanguageModel"

[tool.setuptools.packages.find]
where = ["."]
include = ["langextract_databricksprovider*"]
