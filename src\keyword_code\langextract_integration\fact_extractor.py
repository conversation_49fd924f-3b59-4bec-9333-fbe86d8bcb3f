"""
Fact extraction service using LangExtract.
This module processes LLM analysis responses to extract structured facts.
"""

import langextract as lx
import textwrap
import os
from typing import Dict, List, Any, Optional
from ..config import logger


class FactExtractor:
    """Service for extracting structured facts from LLM analysis responses."""

    def __init__(self, model_id: str = "databricks-llama-4-maverick"):
        """
        Initialize the fact extractor.

        Args:
            model_id: The model ID to use for extraction (using Databricks as default)
        """
        self.model_id = model_id

        # Import the custom provider to ensure it's registered
        try:
            import langextract_databricksprovider
            logger.info("Successfully imported Databricks provider for LangExtract")
        except ImportError as e:
            logger.error(f"Failed to import Databricks provider: {e}")

        # Check for API keys
        if model_id.startswith("databricks") and not os.environ.get('DATABRICKS_API_KEY'):
            logger.warning("DATABRICKS_API_KEY not found in environment variables")
        elif model_id.startswith("gemini") and not os.environ.get('GEMINI_API_KEY'):
            logger.warning("GEMINI_API_KEY not found in environment variables")
        elif model_id.startswith("gpt") and not os.environ.get('OPENAI_API_KEY'):
            logger.warning("OPENAI_API_KEY not found in environment variables")
    
    def extract_facts_from_analysis(self, analysis_text: str, sub_prompt: str, context: str = "") -> Optional[Dict[str, Any]]:
        """
        Extract structured facts from an analysis text using LangExtract.
        
        Args:
            analysis_text: The LLM analysis text to extract facts from
            sub_prompt: The original sub-prompt that generated this analysis
            context: Additional context about the analysis
            
        Returns:
            Dictionary containing extracted facts or None if extraction fails
        """
        try:
            # Define the extraction prompt
            prompt = textwrap.dedent("""
                Extract key facts and information from the provided analysis text.
                Focus on:
                - Specific data points (numbers, dates, amounts, percentages)
                - Key entities (names, organizations, locations)
                - Important relationships and connections
                - Factual statements and conclusions
                - Any quantitative or qualitative findings
                
                Extract only factual information that is explicitly stated in the analysis.
                Do not infer or add information that is not directly present.
            """)
            
            # Create examples to guide the extraction
            examples = [
                lx.data.ExampleData(
                    text=textwrap.dedent("""
                        The loan agreement specifies a total amount of $500 million with an interest rate of 3.5% per annum. 
                        The maturity date is set for December 31, 2030. The borrower is ABC Corporation, 
                        and the lender is XYZ Bank. The loan includes a refinancing component of $300 million 
                        and a greenfield component of $200 million.
                    """),
                    extractions=[
                        lx.data.Extraction(
                            extraction_class="financial_amount",
                            extraction_text="$500 million",
                            attributes={"type": "total_loan_amount", "currency": "USD"}
                        ),
                        lx.data.Extraction(
                            extraction_class="interest_rate",
                            extraction_text="3.5% per annum",
                            attributes={"type": "annual_rate", "value": "3.5"}
                        ),
                        lx.data.Extraction(
                            extraction_class="date",
                            extraction_text="December 31, 2030",
                            attributes={"type": "maturity_date", "format": "full_date"}
                        ),
                        lx.data.Extraction(
                            extraction_class="entity",
                            extraction_text="ABC Corporation",
                            attributes={"role": "borrower", "type": "corporation"}
                        ),
                        lx.data.Extraction(
                            extraction_class="entity",
                            extraction_text="XYZ Bank",
                            attributes={"role": "lender", "type": "bank"}
                        ),
                        lx.data.Extraction(
                            extraction_class="financial_amount",
                            extraction_text="$300 million",
                            attributes={"type": "refinancing_component", "currency": "USD"}
                        ),
                        lx.data.Extraction(
                            extraction_class="financial_amount",
                            extraction_text="$200 million",
                            attributes={"type": "greenfield_component", "currency": "USD"}
                        ),
                    ]
                )
            ]
            
            # Prepare the input text with context
            input_text = f"""
            Original Question: {sub_prompt}
            
            Analysis Text:
            {analysis_text}
            
            Additional Context: {context}
            """
            
            # Run the extraction
            logger.info(f"Running fact extraction for sub-prompt: {sub_prompt[:50]}...")

            # Use the current LangExtract API
            try:
                result = lx.extract(
                    text_or_documents=input_text,
                    prompt_description=prompt,
                    examples=examples,
                    model_id=self.model_id,
                )
            except Exception as api_error:
                logger.error(f"LangExtract API error: {api_error}")
                # If the API call fails, return a simplified extraction
                return {
                    "sub_prompt": sub_prompt,
                    "original_analysis": analysis_text,
                    "extracted_facts": [],
                    "extraction_metadata": {
                        "model_used": self.model_id,
                        "total_extractions": 0,
                        "error": str(api_error)
                    }
                }
            
            # Process the results
            if result and hasattr(result, 'extractions') and result.extractions:
                extracted_facts = {
                    "sub_prompt": sub_prompt,
                    "original_analysis": analysis_text,
                    "extracted_facts": [],
                    "extraction_metadata": {
                        "model_used": self.model_id,
                        "total_extractions": len(result.extractions)
                    }
                }
                
                for extraction in result.extractions:
                    fact = {
                        "category": extraction.extraction_class,
                        "text": extraction.extraction_text,
                        "attributes": extraction.attributes or {}
                    }
                    extracted_facts["extracted_facts"].append(fact)
                
                logger.info(f"Successfully extracted {len(result.extractions)} facts")
                return extracted_facts
            else:
                logger.warning("No facts extracted from analysis text")
                return None
                
        except Exception as e:
            logger.error(f"Error during fact extraction: {e}", exc_info=True)
            return None
    
    def extract_facts_from_multiple_analyses(self, analyses: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract facts from multiple analysis results.
        
        Args:
            analyses: List of analysis dictionaries containing analysis text and metadata
            
        Returns:
            List of dictionaries containing extracted facts for each analysis
        """
        results = []
        
        for analysis in analyses:
            try:
                analysis_text = analysis.get("analysis_summary", "")
                sub_prompt = analysis.get("sub_prompt_analyzed", "")
                context = analysis.get("analysis_context", "")
                
                if not analysis_text:
                    logger.warning(f"No analysis text found for sub-prompt: {sub_prompt}")
                    continue
                
                extracted_facts = self.extract_facts_from_analysis(
                    analysis_text=analysis_text,
                    sub_prompt=sub_prompt,
                    context=context
                )
                
                if extracted_facts:
                    # Add original analysis metadata
                    extracted_facts["original_analysis_metadata"] = {
                        "title": analysis.get("title", ""),
                        "supporting_quotes": analysis.get("supporting_quotes", [])
                    }
                    results.append(extracted_facts)
                    
            except Exception as e:
                logger.error(f"Error processing analysis for fact extraction: {e}")
                continue
        
        logger.info(f"Completed fact extraction for {len(results)} analyses")
        return results

    def extract_facts_from_text(self, text: str, context: str = "") -> Dict[str, Any]:
        """
        Extract facts from a single text (e.g., analysis section).

        Args:
            text: The text to extract facts from
            context: Additional context about the text

        Returns:
            Dictionary containing extracted facts and metadata
        """
        try:
            import langextract as lx

            # Create extraction prompt
            extraction_prompt = f"""
            Extract structured facts from the following analysis text.

            Context: {context}

            Text to analyze:
            {text}

            Please extract key facts including:
            - Financial amounts, dates, percentages
            - Legal entities, parties, jurisdictions
            - Key terms, definitions, requirements
            - Important deadlines, conditions, obligations

            Format each fact with:
            - text: The factual statement
            - category: Type of fact (financial, legal, temporal, etc.)
            - attributes: Relevant details (amount, date, entity, etc.)
            """

            # Use the same examples as the main extractor
            examples = [
                {
                    "text": "The investment amount is $50 million with a 15% annual return expected by December 2024.",
                    "extracted_facts": [
                        {
                            "text": "Investment amount is $50 million",
                            "category": "financial",
                            "attributes": {"amount": "$50 million", "type": "investment"}
                        },
                        {
                            "text": "Expected annual return of 15%",
                            "category": "financial",
                            "attributes": {"percentage": "15%", "type": "return", "frequency": "annual"}
                        },
                        {
                            "text": "Expected completion by December 2024",
                            "category": "temporal",
                            "attributes": {"date": "December 2024", "type": "deadline"}
                        }
                    ]
                }
            ]

            # Perform extraction
            result = lx.extract(
                model=self.model_id,
                examples=examples,
                data=extraction_prompt
            )

            # Process and return results
            extracted_facts = []
            if hasattr(result, 'extracted_facts') and result.extracted_facts:
                extracted_facts = result.extracted_facts
            elif isinstance(result, dict) and 'extracted_facts' in result:
                extracted_facts = result['extracted_facts']
            elif isinstance(result, list):
                extracted_facts = result

            return {
                "extracted_facts": extracted_facts,
                "extraction_metadata": {
                    "model_used": self.model_id,
                    "total_extractions": len(extracted_facts),
                    "context": context
                }
            }

        except Exception as e:
            logger.error(f"Error extracting facts from text: {e}", exc_info=True)
            return {
                "extracted_facts": [],
                "extraction_metadata": {
                    "model_used": self.model_id,
                    "total_extractions": 0,
                    "error": str(e),
                    "context": context
                }
            }
