# Python
__pycache__/
*.py[cod]
*$py.class
*.so

# Distribution / packaging
build/
dist/
*.egg-info/
.eggs/
*.egg

# Virtual environments
.env
.venv
env/
venv/
ENV/

# Testing & coverage
.pytest_cache/
.tox/
htmlcov/
.coverage
.coverage.*

# Type checking
.mypy_cache/
.dmypy.json
dmypy.json
.pytype/

# IDEs
.idea/
.vscode/
*.swp
*.swo

# OS-specific
.DS_Store
Thumbs.db

# Logs
*.log

# Temp files
*.tmp
*.bak
*.backup
