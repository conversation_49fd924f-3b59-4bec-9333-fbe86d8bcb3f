# Copyright 2025 Google LLC.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Compatibility shim for langextract.registry imports."""
# pylint: disable=duplicate-code

from __future__ import annotations

import warnings

from langextract import plugins


def __getattr__(name: str):
  """Forward to plugins module with deprecation warning."""
  warnings.warn(
      "`langextract.registry` is deprecated and will be removed in v2.0.0; "
      "use `langextract.plugins` instead.",
      FutureWarning,
      stacklevel=2,
  )
  return getattr(plugins, name)
