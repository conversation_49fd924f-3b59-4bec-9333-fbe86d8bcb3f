---
name: Feature Request
about: Suggest an idea or improvement
title: 'Request: <brief title of your feature request>'
labels: 'enhancement', 'needs triage'
assignees: ''
---

## Describe the overall idea and motivation

Provide a clear summary of the idea and what use cases it's addressing.

## Related to an issue?

Is this addressing a known / documented issue? If so, which one?

## Possible solutions and alternatives

Do you already have an idea of how the solution should work? If so, document
that here.

Also, if there are alternatives, please document those as well.

## Priority and timeline considerations

Is this time sensitive? Is it a nice to have? Please describe what priority you
feel this should have and why. We'll take this into advisement as we go through
our internal prioritization process.

## Additional context

Is there anything else to consider that wasn't covered by the above?

Would you like to contribute to the project and work on this request?
